/**
 * 白云电气设备数字孪生系统 - 图表配置文件
 * 包含所有图表的配置和初始化逻辑
 */

// 声明全局图表变量，以便在resize事件中访问
var chart1, chart2, chart3, chart4, harmonicCurrentChart, harmonicVoltageChart;

/**
 * 初始化所有图表
 * 页面加载后调用此函数
 */
function initCharts() {
  console.log('[图表初始化] 开始初始化所有图表');

  // 显示所有图表容器
  var chartContainers = document.querySelectorAll('.chart-container');
  chartContainers.forEach(function(container) {
    console.log('[图表初始化] 显示图表容器:', container.id);
    container.style.display = 'block';
  });

  // 只初始化谐波图表（其他图表暂时不需要）
  try {
    // 初始化谐波图表
    initHarmonicCurrentChart();
    initHarmonicVoltageChart();
    console.log('[图表初始化] 谐波图表初始化完成');
  } catch (error) {
    console.error('[图表初始化] 图表初始化失败:', error);
  }

  console.log('[图表初始化] 所有图表初始化完成');
}

/**
 * 初始化设备运行状态图表（仪表盘）
 */
function initDeviceStatusChart() {
  console.log('[图表初始化] 开始初始化设备运行状态图表');
  
  chart1 = echarts.init(document.getElementById('chart1-content'));
  var option1 = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [{
      name: '设备状态',
      type: 'gauge',
      detail: { formatter: '{value}%' },
      data: [{ value: 87, name: '运行状态' }],
      axisLine: {
        lineStyle: {
          color: [[0.3, '#ff4500'], [0.7, '#ffca28'], [1, '#00e676']]
        }
      }
    }]
  };
  chart1.setOption(option1);
  
  console.log('[图表初始化] 设备运行状态图表初始化完成');
}

/**
 * 初始化温度监测图表（折线图）
 */
function initTemperatureChart() {
  console.log('[图表初始化] 开始初始化温度监测图表');
  
  chart2 = echarts.init(document.getElementById('chart2-content'));
  var option2 = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} °C'
      }
    },
    series: [{
      name: '温度',
      type: 'line',
      data: [28, 32, 36, 38, 35, 30, 28],
      areaStyle: {},
      itemStyle: {
        color: '#ff7043'
      },
      lineStyle: {
        width: 3
      }
    }]
  };
  chart2.setOption(option2);
  
  console.log('[图表初始化] 温度监测图表初始化完成');
}

/**
 * 初始化电压监测图表（柱状图）
 */
function initVoltageChart() {
  console.log('[图表初始化] 开始初始化电压监测图表');
  
  chart3 = echarts.init(document.getElementById('chart3-content'));
  var option3 = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} V'
      }
    },
    series: [{
      name: '电压',
      type: 'bar',
      data: [220, 218, 221, 219, 220, 221, 217],
      itemStyle: {
        color: '#29b6f6'
      }
    }]
  };
  chart3.setOption(option3);
  
  console.log('[图表初始化] 电压监测图表初始化完成');
}

/**
 * 初始化系统负载图表（饼图）
 */
function initSystemLoadChart() {
  console.log('[图表初始化] 开始初始化系统负载图表');
  
  chart4 = echarts.init(document.getElementById('chart4-content'));
  var option4 = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: '#fff'
      }
    },
    series: [{
      name: '系统负载',
      type: 'pie',
      radius: '70%',
      data: [
        { value: 40, name: '设备A' },
        { value: 25, name: '设备B' },
        { value: 20, name: '设备C' },
        { value: 15, name: '设备D' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart4.setOption(option4);
  
  console.log('[图表初始化] 系统负载图表初始化完成');
}

/**
 * 初始化谐波电流图表
 */
function initHarmonicCurrentChart() {
  console.log('[图表初始化] 开始初始化谐波电流图表');

  const chartElement = document.getElementById('harmonic-current-content');
  if (!chartElement) {
    console.warn('[图表初始化] 谐波电流图表容器未找到');
    return;
  }

  // 确保容器有尺寸
  if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
    console.warn('[图表初始化] 图表容器尺寸为0，延迟初始化');
    setTimeout(() => initHarmonicCurrentChart(), 500);
    return;
  }

  harmonicCurrentChart = echarts.init(chartElement);
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '谐波电流分析',
      textStyle: {
        color: '#00d4ff',
        fontSize: 14,
        fontWeight: 'bold'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#00d4ff',
          width: 2
        }
      },
      backgroundColor: 'rgba(26, 31, 46, 0.95)',
      borderColor: '#00d4ff',
      borderWidth: 2,
      borderRadius: 8,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: function(params) {
        let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].name + '</div>';
        params.forEach(function(item) {
          result += '<div style="margin:2px 0;"><span style="color:' + item.color + ';font-size:14px;">●</span> ' +
                   '<span style="margin-left:5px;">' + item.seriesName + ': <strong>' + item.value + 'A</strong></span></div>';
        });
        return result;
      }
    },
    grid: {
      left: '10%',
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1次', '3次', '5次', '7次', '9次', '11次', '13次', '15次', '17次'],
      axisLine: {
        lineStyle: {
          color: '#00d4ff',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 12,
        fontWeight: 500
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00d4ff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '电流(A)',
      nameTextStyle: {
        color: '#00d4ff',
        fontSize: 12,
        fontWeight: 600
      },
      axisLine: {
        lineStyle: {
          color: '#00d4ff',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 11,
        formatter: '{value}A'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 212, 255, 0.15)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00d4ff'
        }
      }
    },
    series: [{
      name: '谐波电流',
      type: 'bar',
      data: [125.8, 15.2, 8.7, 4.3, 2.1, 1.8, 1.2, 0.9, 0.6],
      barWidth: '60%',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#66e0ff' },
          { offset: 0.5, color: '#00d4ff' },
          { offset: 1, color: '#0099cc' }
        ]),
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 212, 255, 0.3)',
        shadowBlur: 10,
        shadowOffsetY: 3
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#99f0ff' },
            { offset: 0.5, color: '#33ddff' },
            { offset: 1, color: '#00bbdd' }
          ]),
          shadowColor: 'rgba(0, 212, 255, 0.5)',
          shadowBlur: 15
        }
      },
      animationDelay: function (idx) {
        return idx * 100;
      }
    }],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 50;
    }
  };
  harmonicCurrentChart.setOption(option);

  console.log('[图表初始化] 谐波电流图表初始化完成');
}

/**
 * 初始化谐波电压图表
 */
function initHarmonicVoltageChart() {
  console.log('[图表初始化] 开始初始化谐波电压图表');

  const chartElement = document.getElementById('harmonic-voltage-content');
  if (!chartElement) {
    console.warn('[图表初始化] 谐波电压图表容器未找到');
    return;
  }

  // 确保容器有尺寸
  if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
    console.warn('[图表初始化] 图表容器尺寸为0，延迟初始化');
    setTimeout(() => initHarmonicVoltageChart(), 500);
    return;
  }

  harmonicVoltageChart = echarts.init(chartElement);
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '谐波电压分析',
      textStyle: {
        color: '#00ff88',
        fontSize: 14,
        fontWeight: 'bold'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#00ff88',
          width: 2
        }
      },
      backgroundColor: 'rgba(26, 31, 46, 0.95)',
      borderColor: '#00ff88',
      borderWidth: 2,
      borderRadius: 8,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: function(params) {
        let result = '<div style="font-weight:bold;margin-bottom:5px;">' + params[0].name + '</div>';
        params.forEach(function(item) {
          result += '<div style="margin:2px 0;"><span style="color:' + item.color + ';font-size:14px;">●</span> ' +
                   '<span style="margin-left:5px;">' + item.seriesName + ': <strong>' + item.value + 'V</strong></span></div>';
        });
        return result;
      }
    },
    grid: {
      left: '10%',
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1次', '3次', '5次', '7次', '9次', '11次', '13次', '15次', '17次'],
      axisLine: {
        lineStyle: {
          color: '#00ff88',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 12,
        fontWeight: 500
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00ff88'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '电压(V)',
      nameTextStyle: {
        color: '#00ff88',
        fontSize: 12,
        fontWeight: 600
      },
      axisLine: {
        lineStyle: {
          color: '#00ff88',
          width: 2
        }
      },
      axisLabel: {
        color: '#b8c5d6',
        fontSize: 11,
        formatter: '{value}V'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 255, 136, 0.15)',
          type: 'dashed'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#00ff88'
        }
      }
    },
    series: [{
      name: '谐波电压',
      type: 'line',
      data: [10500, 315, 210, 147, 105, 84, 63, 42, 28],
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#00ff88' },
          { offset: 0.5, color: '#66ffaa' },
          { offset: 1, color: '#99ffcc' }
        ]),
        width: 4,
        shadowColor: 'rgba(0, 255, 136, 0.3)',
        shadowBlur: 10,
        shadowOffsetY: 3
      },
      itemStyle: {
        color: '#00ff88',
        borderColor: '#ffffff',
        borderWidth: 3,
        shadowColor: 'rgba(0, 255, 136, 0.5)',
        shadowBlur: 8
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(0, 255, 136, 0.4)' },
          { offset: 0.5, color: 'rgba(0, 255, 136, 0.2)' },
          { offset: 1, color: 'rgba(0, 255, 136, 0.05)' }
        ])
      },
      emphasis: {
        itemStyle: {
          color: '#66ffaa',
          borderColor: '#ffffff',
          borderWidth: 4,
          shadowColor: 'rgba(0, 255, 136, 0.8)',
          shadowBlur: 15
        },
        lineStyle: {
          width: 5
        }
      },
      animationDelay: function (idx) {
        return idx * 100;
      }
    }],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 50;
    }
  };
  harmonicVoltageChart.setOption(option);

  console.log('[图表初始化] 谐波电压图表初始化完成');
}

/**
 * 调整所有图表大小
 * 在窗口大小变化时调用
 */
function resizeAllCharts() {
  if (chart1) chart1.resize();
  if (chart2) chart2.resize();
  if (chart3) chart3.resize();
  if (chart4) chart4.resize();
  if (harmonicCurrentChart) harmonicCurrentChart.resize();
  if (harmonicVoltageChart) harmonicVoltageChart.resize();
}

/**
 * 更新谐波图表数据
 * 模拟实时数据更新
 */
function updateHarmonicCharts() {
  // 更新谐波电流数据
  if (harmonicCurrentChart) {
    const currentData = [
      125.8 + (Math.random() - 0.5) * 5,
      15.2 + (Math.random() - 0.5) * 2,
      8.7 + (Math.random() - 0.5) * 1,
      4.3 + (Math.random() - 0.5) * 0.5,
      2.1 + (Math.random() - 0.5) * 0.3,
      1.8 + (Math.random() - 0.5) * 0.2,
      1.2 + (Math.random() - 0.5) * 0.2,
      0.9 + (Math.random() - 0.5) * 0.1,
      0.6 + (Math.random() - 0.5) * 0.1
    ];

    harmonicCurrentChart.setOption({
      series: [{
        data: currentData.map(val => Math.max(0, val.toFixed(1)))
      }]
    });
  }

  // 更新谐波电压数据
  if (harmonicVoltageChart) {
    const voltageData = [
      10500 + (Math.random() - 0.5) * 200,
      315 + (Math.random() - 0.5) * 30,
      210 + (Math.random() - 0.5) * 20,
      147 + (Math.random() - 0.5) * 15,
      105 + (Math.random() - 0.5) * 10,
      84 + (Math.random() - 0.5) * 8,
      63 + (Math.random() - 0.5) * 6,
      42 + (Math.random() - 0.5) * 4,
      28 + (Math.random() - 0.5) * 3
    ];

    harmonicVoltageChart.setOption({
      series: [{
        data: voltageData.map(val => Math.max(0, val.toFixed(0)))
      }]
    });
  }
}

// 启动谐波数据更新定时器
setInterval(updateHarmonicCharts, 3000); // 每3秒更新一次
